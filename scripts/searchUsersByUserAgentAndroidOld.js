const moment = require('moment');
const User = require('../rest/model/audb/User');
const UserLogLive = require('../rest/model/audb/UserLogLive');
const UserLogRecord = require('../rest/model/audb/UserLogRecord');
const UserLogVod = require('../rest/model/audb/UserLogVod');
const UserLogLogin = require('../rest/model/audb/UserLogLogin');
const config = require('../config');
const PaymentBlacklist = require('../rest/model/audb/PaymentBlacklist');
const Stealer = require('../rest/model/audb/Stealer');
const { MailTemplatePostmark } = require('../rest/model/audb/MailTemplate');

const logModels = [UserLogLive, UserLogRecord, UserLogVod, UserLogLogin];

async function getActiveUserIds(ignoredUserIds) {
  const timeFrom = moment({ year: 2025, month: 3, day: 15 }).unix();
  const users = await User.find({ expires: { $gt: timeFrom }, id: { $nin: ignoredUserIds } }, { id: 1 }).lean();
  const uids = users.map(user => user.id);

  return uids;
}

async function getStealerUserIds() {
  const logs = await Stealer.find({ uid: { $exists: true } }, { _id: 0, uid: 1 }).lean();
  const userIds = logs.map(log => log.uid);

  return userIds;
}

async function getBlacklistedUserIds() {
  const logs = await PaymentBlacklist.find({ uid: { $exists: true } }, { _id: 0, uid: 1 }).lean();
  const userIds = logs.map(log => log.uid);

  return userIds;
}

async function getOldAndroidAppsUserIds(activeUsersIds, isTv = true) {
  const androidTVRegex = /android\.TV:ver-\d+\.\d+\.\d+/i;
  const androidMobileRegex = /android\.MOBILE_APP:ver-\d+\.\d+\.\d+/i;
  const regex = isTv ? androidTVRegex : androidMobileRegex;
  const matchFilters = [
    { uid: { $in: activeUsersIds } },
    { agent: { $regex: regex } },
  ];
  const timeFrom = moment({ year: 2025, month: 5, day: 1 }).unix();

  const agregation = [
    { $match: { $and: [
      { playtime: { $gt: timeFrom } },
    ] } },
    { $match: { $and: matchFilters } },
    {
      $group: {
        _id: {
          agent: "$agent",
          userId: "$uid"
        },
      }
    },
    {
      $group: {
        _id: "$_id.userId",
        uniqueAgents: { $addToSet: "$_id.agent" }
      }
    },
    {
      $project: {
        _id: 0,
        uid: "$_id",
        uniqueAgents: 1
      }
    }
  ];

  const allResults = [];

  for (const logModel of logModels) {
    const results = await logModel.aggregate(agregation).exec();
    allResults.push(...results);
  }

  const userGroups = allResults.reduce((acc, { uid, uniqueAgents }) => {
    if (!acc[uid]) {
      acc[uid] = new Set();
    }

    uniqueAgents.forEach(agent => acc[uid].add(agent));

    return acc;
  }, {});

  const filteredUids = Object.keys(userGroups).filter(uid => Array.from(userGroups[uid]).some(agent => {
    const tvMatch = agent.match(/vc-(\d+)/);
    const mobileMatch = agent.match(/vc-(\d+)/);
    const match = isTv ? tvMatch : mobileMatch;
    const version = isTv ? 273 : 610;

    if (mobileMatch) {
      const vcValue = parseInt(match[1], 10);

      return vcValue < version;
    }

    return false;
  }));

  return filteredUids;
}

async function getUsersEmailList(oldAndroidAppsUsersIds) {
  const result = await User.find({ id: { $in: oldAndroidAppsUsersIds } }, { em: 1, name: 1 }).exec();
  const users = result.map((user) => {
    user.email = User.decryptEmail(user.em);
    delete user.em;

    return user;
  });

  return users;
}

async function sendUsersEmails(users, isTv) {
  const emailTag = isTv ? 'android_tv' : 'android_mobile';
  const mailTemplate = await MailTemplatePostmark.findOne().byTag(emailTag).exec();

  if (!mailTemplate) return null;

  const BATCH_SIZE = 500;
  const responses = [];

  try {
    for (let i = 0; i < users.length; i += BATCH_SIZE) {
      const batchUsers = users.slice(i, i + BATCH_SIZE);
      const templates = batchUsers.map(user => ({
        From: config.email.noReply,
        TemplateId: mailTemplate.postmarkid,
        To: user.email,
        TemplateModel: { name: user.name },
        ReplyTo: null,
      }));

      if (templates && templates.length) {
        const response = await mailTemplate.sendBatch(templates);
        console.log(`Batch ${i / BATCH_SIZE + 1} sent`, response);
        responses.push(...response);
      }
    }
  } catch (e) {
    console.error('Error sending batch emails:', e);
  }

  return responses;
}

const run = async () => {
  const isTv = false;
  const blacklistedUserIds = await getBlacklistedUserIds();
  const stealerUserIds = await getStealerUserIds();
  const ignoredUserIds = [...blacklistedUserIds, ...stealerUserIds];
  const activeUsersIds = await getActiveUserIds(ignoredUserIds);

  const oldAndroidAppsUsersIds = await getOldAndroidAppsUserIds(activeUsersIds, isTv);
  const users = await getUsersEmailList(oldAndroidAppsUsersIds);
  const responses = await sendUsersEmails(users, isTv);

  return responses;
};

run().then((result) => {
  console.log(`DONE: ${result}`);
  process.exit(0);
}).catch((err) => {
  console.log(err);
  process.exit(1);
});
