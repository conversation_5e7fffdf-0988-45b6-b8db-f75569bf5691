const mongoose = require('mongoose');
const formatPlugin = require('mongoose-model-format');
const paginator = require('mongoose-paginate');
const format = require('../../service/prepare/old/userLogRecord');
const connection = require('./connection');
const ALog = require('./abstract/ALog');
const UserLogRecord = require('./class/UserLogRecord');

const UserLogRecordSchema = mongoose.Schema(Object.assign({}, ALog.abstractSchema, {
  time: { type: Number, required: true },
  client: { type: Number, required: true },
  dkey: { type: String, required: true },
  encode: { type: mongoose.Schema.Types.Mixed, default: null },
  expires: { type: Number, required: true },
  forwardfor: [{ type: String }],
  ips: [{ type: String, required: true }],
  max: { type: Number, required: true },
  proxyip: { type: String, required: true },
  sip: { type: String, required: true },
  serverip: { type: String, required: true },
  salttime: { type: Number, required: true },
  channel: { type: Number, required: true },
  chname: { type: String, required: true },
  showname: { type: String, required: true },
  showtime: { type: Number, required: true },
  showId: { type: mongoose.Schema.Types.ObjectId, required: false },
  // need to remove "show" filed object from the model after 01.07.2026
  show: { type: Object, required: false },
  // show: {
  //   _id: { type: mongoose.Schema.Types.ObjectId },
  //   channel: { type: Number, required: true },
  //   rdatetime: { type: Number, required: true },
  //   rdate: { type: Number, required: true },
  //   time: { type: String, required: true },
  //   name: { type: String, required: true },
  //   name_en: { type: String },
  //   description: { type: String },
  //   description_en: { type: String },
  //   wday: { type: Number, required: true },
  //   genre: { type: String, required: false },
  //   genre_en: { type: String, required: false },
  //   lengthtime: { type: Number, required: true },
  //   weekno: { type: Number, required: true },
  //   showpic: { type: String },
  // },
  player: {
    mdkey: { type: String },
    uid: { type: Number },
    ua: { type: String },
    name: { type: String },
    playerid: { type: Number },
    live: { type: Number },
    record: { type: Number },
    player: { type: Number },
    flash: { type: Number },
    memo: { type: String },
    created: { type: Number },
    playerstyle: { type: mongoose.Schema.Types.Mixed, default: null },
  },
  bookfiles: [],
  asktime: { type: Number },
  blockedOldApp: { type: String, required: false },
  suggestedAppTag: { type: String, required: false },
}), {
  collection: 'bookarchive',
});
UserLogRecordSchema.loadClass(UserLogRecord);
UserLogRecordSchema.plugin(paginator);
UserLogRecordSchema.plugin(formatPlugin);
UserLogRecordSchema.setFormat(format);
UserLogRecordSchema.virtual('Channel', {
  ref: 'Channel',
  localField: 'channel',
  foreignField: 'id',
  justOne: true,
});
UserLogRecordSchema.virtual('Show', {
  ref: 'Schedule',
  localField: 'showId',
  foreignField: '_id',
  justOne: true,
});

/**
 * collection: 'bookarchive'
 * @type {Model<Document>}
 */
module.exports = connection.model('UserLogRecord', UserLogRecordSchema);
