const mongoose = require('mongoose');
const formatPlugin = require('mongoose-model-format');
const paginator = require('mongoose-paginate');
const format = require('../../service/prepare/old/userLogLive');
const connection = require('./connection');
const UserLogLive = require('./class/UserLogLive');
const ALog = require('./abstract/ALog');

const UserLogLiveSchema = mongoose.Schema(Object.assign({}, ALog.abstractSchema, {
  time: { type: Number, required: false },
  client: { type: Number, required: false },
  dkey: { type: String, required: false },
  encode: { type: String, required: false },
  expires: { type: Number, required: false },
  forwardfor: [{ type: String }],
  ips: [{ type: String, required: false }],
  max: { type: Number, required: false },
  proxyip: { type: String, required: false },
  sip: { type: String, required: false },
  serverip: { type: String, required: false },
  salttime: { type: Number, required: false },
  channel: { type: Number, required: false },
  chname: { type: String, required: false },
  showname: { type: String, required: false },
  showtime: { type: Number, required: false },
  showId: { type: mongoose.Schema.Types.ObjectId, required: false },
  // need to remove "show" filed object from the model after 01.07.2026
  show: { type: Object, required: false },
  // show: {
  //   _id: { type: mongoose.Schema.Types.ObjectId, required: false },
  //   channel: { type: Number, required: false },
  //   rdatetime: { type: Number, required: false },
  //   rdate: { type: Number, required: false },
  //   time: { type: String, required: false },
  //   name: { type: String, required: false },
  //   name_en: { type: String, required: false },
  //   description: { type: String, required: false },
  //   description_en: { type: String, required: false },
  //   wday: { type: Number, required: false },
  //   genre: { type: String, required: false },
  //   genre_en: { type: String, required: false },
  //   lengthtime: { type: Number, required: false },
  //   weekno: { type: Number, required: false },
  // },
  player: {
    mdkey: { type: String, required: false },
    uid: { type: Number, required: false },
    ua: { type: String, required: false },
    name: { type: String, required: false },
    playerid: { type: Number, required: false },
    live: { type: Number, required: false },
    record: { type: Number, required: false },
    player: { type: Number, required: false },
    flash: { type: Number, required: false },
    memo: { type: String, required: false },
    created: { type: Number, required: false },
    playerstyle: { type: mongoose.Schema.Types.Mixed, default: null },
  },
  blockedOldApp: { type: String, required: false },
  suggestedAppTag: { type: String, required: false },
}), {
  collection: 'booklive',
});
UserLogLiveSchema.loadClass(UserLogLive);
UserLogLiveSchema.plugin(paginator);
UserLogLiveSchema.plugin(formatPlugin);
UserLogLiveSchema.setFormat(format);
UserLogLiveSchema.virtual('Channel', {
  ref: 'Channel',
  localField: 'channel',
  foreignField: 'id',
  justOne: true,
});
UserLogLiveSchema.virtual('Show', {
  ref: 'Schedule',
  localField: 'showId',
  foreignField: '_id',
  justOne: true,
});

/**
 * collection: 'booklive'
 * @type {Model<Document>}
 */
module.exports = connection.model('UserLogLive', UserLogLiveSchema);
