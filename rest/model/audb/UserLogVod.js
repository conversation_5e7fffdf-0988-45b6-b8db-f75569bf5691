const mongoose = require('mongoose');
const paginator = require('mongoose-paginate');
const connection = require('./connection');
const ALog = require('./abstract/ALog');
const UserLogVod = require('./class/UserLogVod');

const UserLogVodSchema = mongoose.Schema(Object.assign({}, ALog.abstractSchema, {
  client: { type: Number, required: true },
  dkey: { type: String, required: true },
  encode: { type: mongoose.Schema.Types.Mixed, default: null },
  expires: { type: Number, required: true },
  forwardfor: [{ type: String }],
  ips: [{ type: String, required: true }],
  max: { type: Number, required: true },
  player: {
    mdkey: { type: String, required: true },
    uid: { type: Number },
    ua: { type: String, required: true },
    name: { type: String },
    playerid: { type: Number },
    live: { type: Number, required: true },
    record: { type: Number, required: true },
    player: { type: Number, required: true },
    flash: { type: Number, required: true },
    memo: { type: String },
    created: { type: Number },
    playerstyle: { type: mongoose.Schema.Types.Mixed, default: null },
  },
  proxyip: { type: String },
  salttime: { type: Number },
  serverip: { type: String },
  time: { type: Number },
  tofile: { type: String },
  vodid: { type: Number, required: true },
  vodname: { type: String, required: true },
  blockedOldApp: { type: String, required: false },
  suggestedAppTag: { type: String, required: false },
}), {
  collection: 'bookvod',
});
UserLogVodSchema.loadClass(UserLogVod);
UserLogVodSchema.plugin(paginator);

/**
 * collection: 'bookvod'
 * @type {Model<Document>}
 */
module.exports = connection.model('UserLogVod', UserLogVodSchema);
