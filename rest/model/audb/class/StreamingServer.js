const url = require('url');
const Buffer = require('buffer').Buffer;
const strtr = require('locutus/php/strings/strtr');
const pathInfo = require('locutus/php/filesystem/pathinfo');
const httpBuildQuery = require('http-build-query');
const getSignedHmac = require('../../../helpers/getSignedHmac');
const md5 = require('../../../helpers/md5');
const ScheduleClass = require('./Schedule');
const generalConfig = require('../../../../config');

const HIGHWIND_SECRET = 'dccab9bd9e0b69c49302025efdf19702';
const URL_TTL = 12 * 3600;
const classToMethod = {
  Channel: 'getLiveUrl',
  Vod: 'getVodItemUrl',
  Schedule: 'getScheduleUrl',
};

function base64urlEncode(string, newVersion = false) {
  const baseData = Buffer.from(string).toString('base64');
  const strTr = strtr(baseData, '+/', '-_');

  return newVersion ? strTr.replace(/=/g, '') : strTr.replace(/=/g, '%3D');
}

class StreamingServer {
  getUrlPrefix() {
    return `https://${this.sip}`;
  }

  getStreamUrlOf(item, user, userIP, userAllIPs, sid, clientHeaders, oldApp = null) {
    return this[classToMethod[item.constructor.modelName]](item, user, userIP, userAllIPs, sid, clientHeaders, oldApp);
  }

  getLiveUrl(channel, user, userIP, userAllIPs, sid, clientHeaders, oldApp = null) {
    const tohdchannel = channel.tohdchannel || '';
    const prefix = `https://${this.sip}`;
    const appendOne = '-1';
    const chid = `${tohdchannel}${appendOne}`;
    const path = `/edge/${chid}.stream/playlist.m3u8`;
    const tail = StreamingServer.sign({ user, path, userAllIPs, chid, clientHeaders, streamingServer: this, oldApp });

    return prefix + path + tail;
  }

  getScheduleUrl(schedule, user, userIP, userAllIPs, sid, oldApp = null) {
    const { startTime, endTime } = ScheduleClass.getServerTime(schedule);

    const prefix = `https://${this.sip}`;
    const path = `/hls-mix/${schedule.Channel.tohdchannel || ''}-1.stream/start/${startTime}/end/${endTime}/index.m3u8`;
    const chid = `${schedule.Channel.tohdchannel || ''}-${schedule.chtime || schedule.rdatetime}`;

    const tail = StreamingServer.sign({ user, path, userAllIPs, chid, streamingServer: this, oldApp });

    return prefix + path + tail;
  }

  getRecordUrl(schedule, channel, endTimeParameter, user, userIP, userAllIPs, sid, clientHeaders, oldApp = null) {
    const { rdatetime, chtime, vodPath } = schedule;
    const { tohdchannel } = channel;
    const { startTime, endTime } = ScheduleClass.getServerTime(schedule, endTimeParameter);

    const prefix = `https://${this.sip}`;
    const path =
      (vodPath && StreamingServer.getVodPath(vodPath)) || `/hls-mix/${tohdchannel || ''}-1.stream/start/${startTime}/end/${endTime}/index.m3u8`;
    const chid = `${tohdchannel || ''}-${chtime || rdatetime}`;

    const tail = StreamingServer.sign({ user, path, userAllIPs, chid, clientHeaders, streamingServer: this, oldApp });

    return prefix + path + tail;
  }

  getVodItemUrl(vod, user, userIP, userAllIPs, sid, clientHeaders, oldApp = null) {
    return this.getVodUrl(vod, user, userAllIPs, sid, clientHeaders, oldApp);
  }

  getVodUrl(vod, user, userAllIPs, sid, clientHeaders, oldApp = null) {
    const { vodlist, starttime: startTime, endtime: endTime, iscut: isCut } = vod;
    const prefix = `https://${this.sip}`;
    const path = StreamingServer.getVodPath(vodlist, startTime, endTime, isCut);
    const tail = StreamingServer.sign({ user, path, userAllIPs, clientHeaders, streamingServer: this, oldApp });

    return prefix + path + tail;
  }

  static getVodPath(vodlist, startTime = 0, endTime = 0, isCut = 0) {
    let vodList = vodlist;
    let vodListPathInfo = pathInfo(vodList);

    if (+isCut === 1) {
      if (startTime > 0 && endTime > 0) vodList = `${vodListPathInfo.dirname}/start/${startTime}/end/${endTime}/${vodListPathInfo.basename}`;
      if (startTime > 0 && endTime < 0) vodList = `${vodListPathInfo.dirname}/start/${startTime}/${vodListPathInfo.basename}`;
      if (startTime <= 0 && endTime > 0) vodList = `${vodListPathInfo.dirname}/end/${endTime}/${vodListPathInfo.basename}`;

      vodListPathInfo = pathInfo(vodList);
    }

    const vodToFile = `/vod3/${vodList}`;

    return `${vodToFile}/index.m3u8`;
  }

  // only speedtest use it, ToDo: check how we can avoid it
  static signHighwind(str) {
    const urlParts = url.parse(str);
    const query = urlParts.query;
    const parts = urlParts.path.split('/');
    delete parts[parts.length - 1];
    const dirname = parts.join('/');
    const ttl = Math.floor(Date.now() / 1000) + URL_TTL;
    let ttlto = `ttl=${ttl}&l=0&pass=${HIGHWIND_SECRET}`;
    ttlto = query ? `?${query}&${ttlto}` : `?${ttlto}`;
    const message = dirname + ttlto;
    const digest = md5(message);

    return `?${query || ''}${query ? '&' : ''}ttl=${ttl}&l=0&token=${digest}`;
  }

  /**
   *
   * @param user Object
   * @param userAllIPs Array of userIPs
   * @param sid string
   * @param chid string
   * @param clientHeaders Object. Headers from PHP web-sites
   * @param streamingServer Object. Current model of StreamingServer
   * @param oldApp string
   *
   * @returns string
   */
  static sign({ user, userAllIPs, chid = '', clientHeaders, streamingServer, oldApp = null }) {
    const expire = URL_TTL;
    const { id: uid, config } = user;
    const uip = base64urlEncode(userAllIPs.join(','), true);
    const head = streamingServer.userIpHeaderName ? base64urlEncode(streamingServer.userIpHeaderName, true) : null;
    const timestamp = Math.floor(Date.now() / 1000);
    const maxConnection = config && config.maxconn ? config.maxconn : 5;
    const skipIpAuth = (config && config.ipauth === true) || streamingServer.ipcheck !== true ? 'yes' : 'no';

    // ! Stealer mark ("g" param) will be used in gate2 to determine if the user is a stealer, and slow down content requests
    const getRandomInt = () => {
      const min = 1;
      const max = 999;

      return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    const stealerMark = user.isStealer && !user.skipStealerStreamingServers ? getRandomInt() : '';

    const signString = `${uid}${timestamp}${expire}${chid}${timestamp}${uip}${head}${maxConnection}${skipIpAuth}${stealerMark}`;

    const misc = getSignedHmac({ str: signString });
    const preparedClientHeaders = JSON.stringify(clientHeaders);
    const clh = preparedClientHeaders && generalConfig.streamingOptions.sendClientHeader ? base64urlEncode(preparedClientHeaders, true) : null;

    const query = {
      misc,
      uid,
      ts: timestamp,
      e: expire,
      lat: timestamp,
      sil: maxConnection,
      sk: skipIpAuth,
      ...(stealerMark ? { g: stealerMark } : {}),
      ...(uip ? { uip } : {}),
      ...(clh ? { clh } : {}),
      ...(head ? { head } : {}),
      ...(chid ? { chid } : {}),
      ...(oldApp ? { oldapp: oldApp } : {}),
    };

    return `?${httpBuildQuery(query)}`;
  }

  static filterUniqueStreamingServers(servers) {
    const keyPairStreamingServerId = {};
    const serversSortingSet = new Set([]);
    servers.forEach((server) => {
      serversSortingSet.add(server._id);
      keyPairStreamingServerId[server._id] = server;
    });
    const serversSorting = Array.from(serversSortingSet);

    const serversForReturn = [];
    serversSorting.forEach((serverId) => {
      serversForReturn.push(keyPairStreamingServerId[serverId]);
    });

    return serversForReturn;
  }
}

module.exports = StreamingServer;
