module.exports = async (
  { resolved, flags, modelKey, user, ISP, countryCode, stateCode, userIP, userAllIPs, sid, clientHeaders, oldApp = null }
) => {
  const instance = resolved[modelKey];
  const streamingServers = await user.getStreamingServers(ISP, countryCode, stateCode, 1);
  const streams = [streamingServers.mainServer, ...streamingServers.secondaryServers]
    .map(server => server.getStreamUrlOf(instance, user, userIP, userAllIPs, sid, clientHeaders, oldApp));
  const item = instance.play ? await instance.play(resolved, flags) : instance.format();

  return { item, streams, streamingServers };
};
