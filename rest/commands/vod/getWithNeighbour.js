const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const { ApiError } = require('@s1/api-errors');
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const moment = require('moment');
const VodSettings = require('../../model/audb/VodSettings');
const sUserVodPosition = require('../../model/subscription/sUserVodPosition');
const sUserFavoriteVod = require('../../model/subscription/sUserFavoriteVod');
const sUserFavoriteTvShow = require('../../model/subscription/sUserFavoriteTvShow');
const categoriesCache = require('../../service/cache/categories');
const { fillSubscriptions } = require('../../service/subscribeForData');
const sendTelegramMessage = require('../../helpers/sendTelegramMessage');
const config = require('../../../config');
const writePlayVodLog = require('./writePlayVodLog');
const UserLogVod = require('../../model/audb/UserLogVod');
const increaseRedisViews = require('../../helpers/increaseRedisViews');

const PREVIOUS = 'previous';

module.exports = async ({ vodId, locale, append, userAllIPs, user, sessionID, countryCode, ISP, stateCode, req }) => {
  let startTime;
  let timeLog;

  if (config.isSlowLog) {
    startTime = Date.now();
    timeLog = {
      api: 'loadvod2',
      request: {
        userId: user ? user.id : null,
        vodId,
        locale,
        append,
        userAllIPs,
        sessionID,
        countryCode,
        ISP,
        stateCode,
      },
      startTime: moment().format('YYYY/MM/DD, h:mm:ss.SSS'),
    };
  }

  const [vod, streamingServers] = await Promise.all([
    Vod.findOne({ id: vodId }).whichOnline().populate('category').exec(),
    user.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD),
  ]);

  if (config.isSlowLog) timeLog.loadVod = Date.now() - startTime;
  if (!vod) throw new ApiError(404, 'Vod not found');

  req.resolved = req.resolved || { vod };
  const vodThumbnailDomain = streamingServers.mainServer.sip;
  const [neighbour, fullPath, vodSettings] = await Promise.all([
    vod.getNeighbour(append === PREVIOUS),
    vod.category.getFullPath().then((fullPath) =>
      Promise.all(
        fullPath.map(async (category) => {
          category.showpic = await category.resolvePicture(categoriesCache, { domain: vodThumbnailDomain });

          return category;
        }),
      ),
    ),
    VodSettings.findOne({ data: 'vodurl' }).exec(),
  ]);
  fullPath.reverse();

  if (config.isSlowLog) timeLog.resolvePictureAndGetNeighbour = Date.now() - startTime;

  const vods = [vod];
  const tvShowCategory = Category.getTvShow(fullPath);

  if (config.isSlowLog) timeLog.getTvShow = Date.now() - startTime;
  // TODO move increase views to the redis and update once per XX mins
  if (tvShowCategory) await increaseRedisViews('vodcate', tvShowCategory.id);
  if (neighbour) vods.push(neighbour);

  const baseCondition = { uid: user.id };
  const subUserVodPosition = await sUserVodPosition({ baseCondition });
  const subUserFavorite = (tvShowCategory ? sUserFavoriteTvShow : sUserFavoriteVod)({ baseCondition });
  [subUserFavorite, subUserVodPosition].forEach((sub) => vods.forEach((vod) => sub.add(vod)));

  if (config.isSlowLog) timeLog.addSubscriptions = Date.now() - startTime;

  await Promise.all([
    fillSubscriptions(),
    Promise.all(vods.map((vod) => vod.prepare({ thumbnailOptions: { domain: streamingServers.mainServer.sip } }))),
  ]);

  if (config.isSlowLog) timeLog.fillSubscriptionsAndPrepareVods = Date.now() - startTime;

  const { oldApp = null } = req;
  const fullLinks = Vod.getFullLinksWithContents(
    vods.map((vod) =>
      vod.format({
        tvShowCategory,
        vodSettings,
        locale,
      }),
    ),
    streamingServers,
    user,
    userAllIPs,
    sessionID,
    req.clIpHeaders,
    oldApp,
  );
  const urlPrefix = streamingServers.mainServer.getUrlPrefix();
  const playlist = vods.map(() => ({
    file: null,
    urlport: urlPrefix,
    start: 0,
    duration: 0,
  }));

  if (config.isSlowLog) timeLog.getFullLinks = Date.now() - startTime;

  await increaseRedisViews('vodm', vod.id);

  // do not save the same channel log for the last 10 mins
  const checkLastXXmins = 10;
  const timeFrom = moment().subtract(checkLastXXmins, 'minutes').unix();
  const lastLogs = await UserLogVod.find({ uid: user.id, playtime: { $gte: timeFrom }, vodid: vodId }, { _id: 1 })
    .cache(checkLastXXmins)
    .lean();

  if (!lastLogs.length) {
    // no need to wait for save logs
    writePlayVodLog({ req, streamingServers, vodId, vod, user, tvShowCategory, vodSettings, fullPath, oldApp });
  }

  const pathinfo = fullPath.map((category) => category.format({ locale }));

  if (config.isSlowLog) timeLog.formatVodAndCategory = Date.now() - startTime;
  if (config.isSlowLog && Date.now() - startTime > config.slowLogTimeout) sendTelegramMessage(timeLog);

  return {
    results: [null],
    istvshow: +!!tvShowCategory,
    pathinfo,
    playlist,
    fulllinks: fullLinks,
  };
};
