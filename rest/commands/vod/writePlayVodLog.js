const UserLogVod = require('../../model/audb/UserLogVod');

module.exports = async ({
  req, streamingServers, vodId, vod, user, tvShowCategory, vodSettings, fullPath, oldApp,
}) => {
  if (user.config && user.config.hasOwnProperty('skipSaveWatchLogs') && user.config.skipSaveWatchLogs) return;

  const locale = 'en';
  const vodinfo = vod.format({ tvShowCategory, vodSettings, locale });
  const pathinfo = fullPath ? fullPath.map(category => category.format({ locale })).reverse() : null;
  let vodNameWithPath = '';

  if (pathinfo && pathinfo.length) {
    pathinfo.forEach((path) => {
      vodNameWithPath += `/${path.name}`;
    });
    vodNameWithPath += `/${vodinfo.name}`;
  } else {
    vodNameWithPath = vodinfo.name;
  }

  await UserLogVod.write({ ...req, streamingServers, vodid: vodId, vodname: vodNameWithPath, tofile: vod.tofile, user, blockedOldApp: oldApp });
};
