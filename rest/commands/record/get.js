const moment = require('moment');
const url = require('url');
const { ExtraPackageNeededError } = require('@s1/api-errors');
const Channel = require('../../model/audb/Channel');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const Schedule = require('../../model/audb/Schedule');
const sendTelegramMessage = require('../../helpers/sendTelegramMessage');
const config = require('../../../config');
const increaseRedisViews = require('../../helpers/increaseRedisViews');

const RECORDS_LIMIT = 4;

function getEndRDateTime(endRDateTime, startRDateTime, afterTime, endTime) {
  if (endRDateTime - startRDateTime < 3600) endRDateTime = startRDateTime + 3600;
  if (endRDateTime > endTime - afterTime) endRDateTime = endTime - afterTime;

  return endRDateTime;
}

module.exports = async ({ channel, startTime, user, userIP, userAllIPs, ISP, countryCode, stateCode, locale, sid, req, isAdmin, fromVod }) => {
  let startDateTime;
  let timeLog;

  if (config.isSlowLog) {
    startDateTime = Date.now();
    timeLog = {
      api: 'loadrecord',
      request: {
        userId: user ? user.id : null,
        channelId: channel ? channel.id : null,
        startTime,
        userIP,
        userAllIPs,
        ISP,
        countryCode,
        stateCode,
        locale,
        sid,
        isAdmin,
      },
      startTime: moment().format('YYYY/MM/DD, h:mm:ss.SSS'),
    };
  }
  if (Array.isArray(startTime)) startTime = startTime[0];

  const [checkExtra, streamingServers] = await Promise.all([
    Channel.checkChannel({ user, channel }),
    user.getStreamingServers(ISP, countryCode, stateCode, 2),
  ]);

  if (config.isSlowLog) timeLog.checkChannel = Date.now() - startDateTime;
  if (!checkExtra) throw new ExtraPackageNeededError(null, { playlist: '', md: '' }, locale);

  const records = await channel.getRecords(startTime, RECORDS_LIMIT, streamingServers.mainServer.sip, user);

  if (config.isSlowLog) timeLog.getRecords = Date.now() - startDateTime;

  let formattedRecords = Schedule.formatAll(records, {
    scheduleChannel: channel,
    locale,
    isAdmin,
    simplify: false,
    setChtime: true,
    setCustomerDate: false,
    fillChannelProperties: false,
    thumbnailOptions: {
      domain: streamingServers.mainServer.sip,
    },
  });

  if (config.isSlowLog) timeLog.formatSchedule = Date.now() - startDateTime;
  // load default schedules for the current and next day to fix wrong play urls
  if (formattedRecords[0].rdatetime > parseInt(startTime)) {
    const hour = parseInt(moment.unix(startTime || new Date()).format('hh'));
    const addDay = hour >= 0 && hour < 6 ? -1 : 0;
    const date = moment.unix(parseInt(startTime) + addDay * 24 * 60 * 60).format('DD/MM/YYYY');
    const dateTomorrow = moment.unix(parseInt(startTime) + (addDay + 1) * 24 * 60 * 60).format('DD/MM/YYYY');
    const schedulesOptions = {
      channelInfo: [channel],
      date,
      isRecord: true,
      thumbnailDomain: streamingServers.mainServer.sip,
      locale,
    };
    let defaultSchedules = Schedule.getDefaultSchedules(schedulesOptions);
    defaultSchedules = defaultSchedules.filter((schedule) => schedule.rdatetime >= parseInt(startTime));
    schedulesOptions.date = dateTomorrow;
    const defaultSchedulesTomorrow = Schedule.getDefaultSchedules(schedulesOptions);
    defaultSchedules = defaultSchedules.concat(defaultSchedulesTomorrow);
    const splitSize = defaultSchedules.length >= 4 ? 4 : defaultSchedules.length;
    formattedRecords = defaultSchedules.slice(0, splitSize).map((defaultSchedule, i) => {
      if (formattedRecords[i]) {
        defaultSchedule.vodPath = formattedRecords[i].vodPath;

        if (formattedRecords[i].showpic) defaultSchedule.showpic = formattedRecords[i].showpic;
      }

      return defaultSchedule;
    });

    if (config.isSlowLog) timeLog.getDefaultSchedule = Date.now() - startDateTime;
  }

  const { afterTime, beforeTime, endTime } = channel.getSchedulesTimings();
  formattedRecords[0].rdatetime -= beforeTime;
  const startRDateTime = formattedRecords[0].rdatetime;
  // const endRDateTime = getEndRDateTime(formattedRecords[formattedRecords.length - 1].rdatetime, startRDateTime, afterTime, endTime);
  const timeToCheck =
    formattedRecords[1].rdatetime + afterTime - startRDateTime <= 3600 ? startRDateTime + 3600 : formattedRecords[1].rdatetime + afterTime;

  if (formattedRecords[1].rdatetime + afterTime - startRDateTime <= 3600) formattedRecords.length = 3;
  if (timeToCheck < endTime) formattedRecords[1].rdatetime = timeToCheck;
  if (formattedRecords[1].rdatetime > endTime) formattedRecords[1].rdatetime = endTime;

  const reducedRecords = formattedRecords.reduce((arr, record, index) => {
    // format picture url time the same as for the play url
    const { startTime, endTime } = Schedule.getServerTime(record);

    if (!record.showpic)
      record.showpic = Schedule.getRecordThumbnailUrl(channel, streamingServers.mainServer.sip, startTime + record.lengthtime / 2, endTime);
    if (index > 1) {
      if (record.rdatetime > formattedRecords[1].rdatetime && record.rdatetime < endTime - afterTime) arr.push(record);
    } else arr.push(record);

    return arr;
  }, []);

  if (config.isSlowLog) timeLog.getRecordThumbnailUrl = Date.now() - startDateTime;

  const playlist = [];
  const fulllinks = [];
  const { oldApp = null } = req;
  [streamingServers.mainServer, ...streamingServers.secondaryServers].forEach((server) => {
    const tmp = [];
    reducedRecords.forEach((schedule, index) => {
      if (!reducedRecords[index + 1]) return;

      const _schedule = Object.assign({}, schedule);
      // eslint-disable-next-line max-len
      _schedule.playurl = server.getRecordUrl(schedule, channel, reducedRecords[index + 1].rdatetime, user, userIP, userAllIPs, sid, req.clIpHeaders, oldApp);
      _schedule.vtturl = _schedule.playurl.replace('hls-mix', 'rthumbs').replace('index.m3u8', 'index.vtt');

      tmp.push(_schedule);
    });
    fulllinks.push(tmp);
  });

  if (config.isSlowLog) timeLog.getFulllinks = Date.now() - startDateTime;
  if (fulllinks[0] && Array.isArray(fulllinks[0]))
    fulllinks[0].forEach((link) => {
      const parseUrl = url.parse(link.playurl);
      playlist.push({
        urlport: streamingServers.mainServer.getUrlPrefix(),
        file: `${parseUrl.pathname}?${parseUrl.query}`,
        start: 0,
      });
    });
  if (req.resolved) Object.assign(req.resolved, { channel, schedule: reducedRecords[0] });
  else req.resolved = req.resolved || { channel, schedule: reducedRecords[0] };

  const show = reducedRecords[0];
  // do not save the same channel log for the last 10 mins
  const checkLastXXmins = 10;
  const timeFrom = moment().subtract(checkLastXXmins, 'minutes').unix();
  // eslint-disable-next-line max-len
  const lastLogs = await UserLogRecord.find(
    { uid: user.id, playtime: { $gte: timeFrom }, channel: channel.id, 'show.rdatetime': show.rdatetime },
    { _id: 1 },
  )
    .cache(checkLastXXmins * 60)
    .lean();

  if (!lastLogs.length && (!user.config || !user.config.hasOwnProperty('skipSaveWatchLogs') || !user.config.skipSaveWatchLogs)) {
    // we should not to wait for saving logs
    UserLogRecord.write({ ...req, channel, streamingServers, user, blockedOldApp: oldApp });
  }
  if (config.isSlowLog) timeLog.finish = Date.now() - startDateTime;
  if (config.isSlowLog && Date.now() - startDateTime > config.slowLogTimeout) sendTelegramMessage(timeLog);

  let filteredLinks = Array.from(new Set(fulllinks));
  filteredLinks = [filteredLinks[0]].concat(filteredLinks);

  if (fromVod) {
    const currentViews = await increaseRedisViews('record', `${show.channel}_${show.chtime}`);
    show.views += currentViews;
  }

  return {
    channel: channel.toJSON(),
    show,
    playlist,
    fulllinks: filteredLinks,
  };
};
