const { ChannelNotFoundError, ExtraPackageNeededError } = require('@s1/api-errors');
const Channel = require('../../model/audb/Channel');
const Schedule = require('../../model/audb/Schedule');
const UserLogLive = require('../../model/audb/UserLogLive');
const UserFavoriteLive = require('../../model/audb/UserFavoriteLive');
const getUnixTimestampRoundedByMinutes = require('../../helpers/getUnixTimestampRoundedByMinutes');

module.exports = async ({
  user, userIP, ISP, countryCode, stateCode, sid, cid, epg, userAllIPs, req, locale,
}) => {
  const cidAsNumber = parseInt(cid);

  if (cidAsNumber <= 0 || Number.isNaN(cidAsNumber)) throw new ChannelNotFoundError(req.__('Channel ID is required'));

  const channel = await Channel.findOne({ id: cid, ifshow: 0 })
    .populate('channelGroup')
    .cache(3600, `channel_id_${cid}_online_with_group`)
    .exec();

  if (!channel) throw new ChannelNotFoundError(req.__('Channel #%s not found', cid));

  req.resolved = req.resolved || { channel };
  const checkExtra = await Channel.checkChannel({ user, channel });

  if (!checkExtra && channel) throw new ExtraPackageNeededError(null, { playlist: '', md: '' }, locale);

  const streamingServers = await user.getStreamingServers(ISP, countryCode, stateCode, 1);
  const userFavorite = await UserFavoriteLive.findOne({ uid: user.id, chvod: cid }, { _id: 1}).lean().exec();
  const shows = epg ? await Schedule.getShowsForEPG(channel) : [];

  const fullLinks = [];
  const clientHeaders = req.clIpHeaders;
  const { oldApp = null } = req;
  const liveUrl = streamingServers.mainServer.getLiveUrl(
    channel,
    user,
    userIP,
    userAllIPs,
    sid,
    clientHeaders,
    oldApp,
  );
  fullLinks.push(liveUrl);

  for (const server of streamingServers.secondaryServers) {
    fullLinks.push(server.getLiveUrl(channel, user, userIP, userAllIPs, sid, clientHeaders, oldApp));
  }

  const mainServerSip = `${streamingServers.mainServer.sip}`;

  // do not save the same channel log for the last 10 mins
  const checkLastXXmins = 10;
  // get last 5 mins interval and extend it to 10 mins for more accurate search
  const timeFrom = getUnixTimestampRoundedByMinutes(checkLastXXmins - 5) - 5 * 60;
  // eslint-disable-next-line max-len
  const lastLog = await UserLogLive.findOne({ uid: user.id, playtime: { $gte: timeFrom }, channel: cid }, { _id: 1 })
    .lean()
    .cache(checkLastXXmins * 60)
    .exec();

  if (!lastLog && (!user.config || !user.config.hasOwnProperty('skipSaveWatchLogs') || !user.config.skipSaveWatchLogs)) {
    // no need to wait write user logs
    UserLogLive.write({
      ...req,
      channel,
      // shows,
      streamingServers,
      encode: fullLinks[0].split(mainServerSip)[1],
      user,
      blockedOldApp: oldApp,
    });
  }

  let filteredLinks = Array.from(new Set(fullLinks));
  filteredLinks = [filteredLinks[0]].concat(filteredLinks);

  return {
    uid: user.id,
    isradio: channel.channelGroup ? channel.channelGroup.isradio : null,
    topass: '', // check the git history for that param, there was depending from server.hmac value. Now hmac encryption is default.
    chname: channel.tohdchannel || null,
    isssl: true,
    isinfav: userFavorite ? 1 : 0,
    fulllinks: filteredLinks,
    ...(epg ? { shows: shows.map(show => Schedule.format(show, {
      scheduleChannel: channel,
      locale,
      thumbnailOptions: { domain: streamingServers.mainServer.sip },
    })) } : {}),
  };
};
