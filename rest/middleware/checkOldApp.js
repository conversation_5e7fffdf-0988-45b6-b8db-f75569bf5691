const OLD_APPS = {
  AndroidMobile: 'am',
  AndroidTV: 'at',
  IosMobile: 'im',
  IosTV: 'it',
  RokuTV: 'rt',
};

module.exports = async function blockProxies(request, response, next) {
  const { headers } = request;

  if (headers && headers['user-agent'] && headers) {
    const userAgent = headers['user-agent'];

    if (/^(?=.*android)(?!.*mozilla)/i.test(userAgent)) {
      if (/android\.TV:ver-\d+\.\d+\.\d+/i.test(userAgent)) {
        const match = userAgent.match(/vc-(\d+)/);

        if (match && match.length > 1) {
          const vcValue = parseInt(match[1], 10);

          // older than vc-2.0.50
          if (vcValue < 273) {
            request.oldApp = OLD_APPS.AndroidTV;
          }
        } else {
          request.oldApp = OLD_APPS.AndroidTV;
        }
      } else if (/android\.MOBILE_APP:ver-\d+\.\d+\.\d+/i.test(userAgent)) {
        const match = userAgent.match(/vc-(\d+)/);

        if (match && match.length > 1) {
          const vcValue = parseInt(match[1], 10);

          // older than vc-3.0.110
          if (vcValue < 610) {
            request.oldApp = OLD_APPS.AndroidMobile;
          }
        } else {
          request.oldApp = OLD_APPS.AndroidMobile;
        }
      } else {}
    }
  }

  return next();
};
