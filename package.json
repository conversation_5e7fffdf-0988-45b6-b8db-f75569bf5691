{"name": "s1-api", "version": "2.1.3", "description": "", "main": "index.js", "engines": {"node": ">=10.16.2 <=10.24.1", "npm": ">=6.14.4"}, "scripts": {"test": "node --expose-gc --max_old_space_size=4096 ./node_modules/.bin/jest --watchAll=false --no-cache --logHeapUsage; node removeTestDbs.js", "start": "nodemon --exec node bin/api", "develop": "pm2 delete s1-api ; pm2 start ecosystem.dev.example.json", "openapi:build": "speccy resolve -j rest/main.yaml > openapi.yaml", "openapi:lint": "speccy lint -s operation-tags -s openapi-tags -j rest/main.yaml", "build:doc": "redoc-cli bundle openapi.yaml -o doc/index.html --title \"S1 API doc\" --cdn", "log:development": "pm2 logs s1-api --raw | pino-elasticsearch -i api_development_%{DATE} -n http://***********:9200", "log:production": "pm2 logs s1-api --raw | pino-elasticsearch -i api_production_%{DATE} -n http://***********:9200"}, "repository": {"type": "git", "url": "git+ssh://*****************/d09e3b9e7f4f/s1-api.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "ISC", "homepage": "https://bitbucket.org/d09e3b9e7f4f/s1-api#readme", "dependencies": {"@faker-js/faker": "^7.6.0", "@s1/api-constants": "^1.0.10", "@s1/api-errors": "^2.0.15", "@s1/assets-storage-client": "^1.0.2", "@s1/config": "^1.0.1", "@s1/expeditious-engine-redis": "^0.1.3", "@s1/is-proxyr": "^1.1.5", "@s1/log": "^2.0.4", "@s1/random-hash": "^1.0.0", "@s1/sign-request": "^1.1.2", "@s1/vod-models": "^1.6.104", "axios": "^0.19.0", "body-parser": "^1.19.0", "bottleneck": "^2.19.5", "buffer": "^5.2.1", "cachegoose": "^7.1.0", "cacheman": "^2.2.1", "cacheman-redis": "^2.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.4", "cookie-signature": "^1.2.0", "cors": "^2.8.5", "cron": "^1.7.1", "crypto": "^1.0.1", "csv-parser": "^2.3.0", "csv-streamify": "^4.0.0", "debug": "^3.2.6", "deep-diff": "^1.0.2", "dotenv": "^5.0.1", "ethereumjs-wallet": "^1.0.2", "express": "^4.17.1", "express-async-errors": "^3.1.1", "express-cache-controller": "^1.1.0", "express-expeditious": "^5.1.0", "express-mongo-sanitize": "^2.1.0", "express-mongoose-resolve": "^1.1.3", "express-redis-cache": "^1.1.3", "express-session": "^1.16.2", "form-data": "^4.0.0", "fraudlabspro-nodejs": "^2.1.0", "html-to-text": "^4.0.0", "http-build-query": "^0.7.0", "https-proxy-agent": "^5.0.1", "i18n": "^0.13.2", "ioredis": "^4.28.5", "ioredis-mock": "^4.12.1", "ip-subnet-calculator": "^1.1.8", "ip-to-int": "^0.3.1", "is-ip": "^2.0.0", "is-local-ip": "^1.1.0", "json-schema-ref-parser": "^7.1.0", "jsonwebtoken": "^8.4.0", "locutus": "^2.0.11", "lodash": "^4.17.15", "mailgun.js": "^8.0.6", "maxmind": "^4.1.1", "mcrypt": "^0.1.17", "mem": "^6.0.1", "memoizee": "^0.4.14", "minizlib": "^1.2.1", "moment": "^2.24.0", "moment-timezone": "^0.5.33", "mongoose": "^5.13.2", "mongoose-aggregate-paginate": "^1.1.3", "mongoose-execution-time": "^1.0.2", "mongoose-lean-virtuals": "^0.9.1", "mongoose-model-format": "^1.0.0", "mongoose-paginate": "^5.0.3", "mongoose-unix-timestamp-plugin": "^0.6.4", "mongoose-uuid": "0.0.2", "morgan": "^1.9.1", "multer": "^1.4.2", "netmask": "^1.0.6", "node-schedule": "^1.3.2", "parse-numeric-range": "^1.3.0", "passport": "^0.4.0", "passport-local": "^1.0.0", "passport-local-token": "^1.0.1", "pdfkit": "^0.11.0", "php-serialize": "^1.3.1", "pino-elasticsearch": "^4.3.0", "postmark": "^2.2.9", "qrcode": "^1.5.0", "query-string": "^6.8.1", "random-item": "^1.0.0", "redis": "^2.8.0", "redis-commands": "^1.5.0", "redis-ip-ranges": "^1.3.6", "redis-mock": "^0.56.3", "request": "^2.88.0", "request-promise-native": "^1.0.7", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2", "socks-proxy-agent": "^7.0.0", "subscribe-for-data": "^1.0.0", "subscribe-for-data-from-mongoose": "^1.0.1", "svg-captcha": "^1.4.0", "swagger-jsdoc": "^4.0.0", "swagger-ui-express": "^4.1.4", "useragent": "^2.3.0", "uuid-mongodb": "^2.1.1", "yauzl": "^2.10.0"}, "devDependencies": {"@types/express": "^4.17.9", "@types/mongoose": "^5.10.1", "@types/node": "^14.14.10", "@types/passport": "^1.0.4", "apidoc": "^0.17.7", "eslint": "^5.16.0", "eslint-config-airbnb": "^17.1.1", "eslint-config-airbnb-base": "^13.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^22.15.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.14.3", "husky": "^1.3.1", "jest": "^27.0.6", "jest-extended": "^0.11.2", "jest-json-schema": "^2.0.2", "lint-staged": "^7.3.0", "mongodb-memory-server": "^6.2.4", "node-fetch": "^2.6.1", "nodemon": "^1.19.1", "present": "^1.0.0", "prettier": "^3.3.3", "redoc-cli": "^0.9.6", "speccy": "^0.11.0", "supertest": "^6.1.3", "unescape": "^1.0.1"}}